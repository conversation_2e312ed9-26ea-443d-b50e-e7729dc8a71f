/**
 * @file    lds_log.h
 * @brief   日志输出模块头文件，提供UART日志输出功能的接口声明
 * <AUTHOR>
 * @version v1.0.0
 * @date    2024
 *
 * @copyright Copyright (c) <PERSON><PERSON><PERSON> ~. All rights reserved.
 */
/* prevent from redefinition ------------------------------------------------ */
#ifndef __SOUNDHOST_APPLICATIONS_LDS_LOG_H__
#define __SOUNDHOST_APPLICATIONS_LDS_LOG_H__

#ifdef __cplusplus
extern "C" {
#endif

/* needed include files ----------------------------------------------------- */
#include <string.h>
#include <stdint.h>
#include <stdio.h>

#include "n32g45x.h"
#include "system_n32g45x.h"

/* macro definition --------------------------------------------------------- */


/* typedef ------------------------------------------------------------------ */


/* global variables declare ------------------------------------------------- */


/* global function declare -------------------------------------------------- */

/**
 * @brief  初始化UART日志输出功能
 * @return 成功返回0
 */
int ldsUartLogInit(void);

/**
 * @brief  通过USART发送数据缓冲区
 * @param  pucData 数据缓冲区指针
 * @param  uiLen   数据长度
 * @return 成功返回0，失败返回-1
 */
int ldsUsartLogSendBuffer(uint8_t *pucData, uint32_t uiLen);

/**
 * @brief  通过USART发送字符串
 * @param  pStr 字符串指针
 * @return 成功返回0，失败返回-1
 */
int ldsUsartLogSendString(char *pStr);

/**
 * @brief  格式化输出日志信息
 * @param  fmt 格式化字符串
 * @param  ... 可变参数列表
 * @return 成功返回0
 */
int ldsUsartLogOutput(char *fmt, ...);

#ifdef __cplusplus
}
#endif

#endif /* __SOUNDHOST_APPLICATIONS_LDS_LOG_H__ */

/**************************** END OF FILE *************************************/
