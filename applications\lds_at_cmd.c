/**
 * @file lds_at_cmd.c
 * @brief AT指令解析模块实现文件
 * @details 实现AT指令解析功能，支持AT+BOOT=MCU和AT+TFWINFO指令
 * <AUTHOR>
 * @date 2024
 */

/* 相关头文件 */
#include "lds_at_cmd.h"
/* C库 */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>
#include <ctype.h>
/* 其他库的.h */
#include <n32g45x.h>
/* 本项目内的.h */
#include "main.h"
#include "lds_log.h"
#include "lds_ymodem.h"
#include "lds_flash.h"

/* 全局变量定义 */
static char g_atCmdBuffer[LDS_AT_CMD_BUFFER_SIZE];  /**< AT指令接收缓冲区 */
static uint16_t g_atCmdBufferIndex = 0;             /**< 缓冲区索引 */
static bool g_bootMcuRequested = false;            /**< 是否收到BOOT=MCU指令 */

/**
 * @brief 串口接收单个字节（非阻塞）
 * @param byte 接收到的字节指针
 * @return true 接收成功, false 无数据
 */
static bool ldsAtCmdReceiveByte(uint8_t* byte)
{
    if (byte == NULL) {
        return false;
    }

    /* 检查是否有数据可读 */
    if (USART_GetFlagStatus(USART1, USART_FLAG_RXDNE) == SET) {
        *byte = (uint8_t)USART_ReceiveData(USART1);
        return true;
    }

    return false;
}

/**
 * @brief 串口发送单个字节
 * @param byte 要发送的字节
 */
static void ldsAtCmdSendByte(uint8_t byte)
{
    /* 等待发送缓冲区空 */
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXDE) == RESET) {
        /* 等待发送缓冲区空闲 */
    }
    USART_SendData(USART1, byte);
}

/**
 * @brief 解析AT指令字符串
 * @param cmdStr 指令字符串
 * @param atCmd 解析结果结构体
 * @return lds_at_cmd_result_t 解析结果
 */
static lds_at_cmd_result_t ldsAtCmdParseString(const char* cmdStr, lds_at_cmd_t* atCmd)
{
    char* equalPos;
    size_t cmdLen;

    if (cmdStr == NULL || atCmd == NULL) {
        return LDS_AT_CMD_RESULT_ERROR;
    }

    /* 清空结构体 */
    memset(atCmd, 0, sizeof(lds_at_cmd_t));

    /* 检查是否以AT+开头 */
    if (strncmp(cmdStr, "AT+", 3) != 0) {
        return LDS_AT_CMD_RESULT_INVALID;
    }

    /* 查找等号位置 */
    equalPos = strchr(cmdStr, '=');
    if (equalPos != NULL) {
        /* 有参数的指令 */
        cmdLen = equalPos - cmdStr;
        if (cmdLen >= sizeof(atCmd->cmd)) {
            return LDS_AT_CMD_RESULT_ERROR;
        }
        strncpy(atCmd->cmd, cmdStr, cmdLen);
        atCmd->cmd[cmdLen] = '\0';

        /* 提取参数 */
        strncpy(atCmd->param, equalPos + 1, sizeof(atCmd->param) - 1);
        atCmd->param[sizeof(atCmd->param) - 1] = '\0';
    } else {
        /* 无参数的指令 */
        strncpy(atCmd->cmd, cmdStr, sizeof(atCmd->cmd) - 1);
        atCmd->cmd[sizeof(atCmd->cmd) - 1] = '\0';
    }

    /* 识别指令类型 */
    if (strcmp(atCmd->cmd, "AT+BOOT") == 0 && strcmp(atCmd->param, "MCU") == 0) {
        atCmd->type = LDS_AT_CMD_BOOT_MCU;
    } else if (strcmp(atCmd->cmd, "AT+TFWINFO") == 0) {
        atCmd->type = LDS_AT_CMD_TFWINFO;
    } else {
        atCmd->type = LDS_AT_CMD_UNKNOWN;
        return LDS_AT_CMD_RESULT_INVALID;
    }

    return LDS_AT_CMD_RESULT_OK;
}

/**
 * @brief 初始化AT指令解析模块
 * @return 0 成功, 其他 失败
 */
int ldsAtCmdInit(void)
{
    /* 清空缓冲区 */
    memset(g_atCmdBuffer, 0, sizeof(g_atCmdBuffer));
    g_atCmdBufferIndex = 0;
    g_bootMcuRequested = false;

    printf("AT command parser initialized\n");
    return 0;
}

/**
 * @brief 发送AT指令响应
 * @param response 响应内容
 * @return true 发送成功, false 发送失败
 */
bool ldsAtCmdSendResponse(const char* response)
{
    if (response == NULL) {
        return false;
    }

    /* 发送响应内容 */
    for (size_t i = 0; i < strlen(response); i++) {
        ldsAtCmdSendByte((uint8_t)response[i]);
    }

    /* 发送回车换行 */
    ldsAtCmdSendByte('\r');
    ldsAtCmdSendByte('\n');

    return true;
}

/**
 * @brief 处理AT+BOOT=MCU指令
 * @return true 处理成功, false 处理失败
 * @note 收到此指令后会启动YModem升级流程
 */
bool ldsAtCmdHandleBootMcu(void)
{
    // printf("Received AT+BOOT=MCU command, preparing for YModem upgrade...\n");
    
    /* 发送OK响应 */
    ldsAtCmdSendResponse("OK");
    
    /* 设置标志，表示收到BOOT=MCU指令 */
    g_bootMcuRequested = true;
    
    return true;
}

/**
 * @brief 处理AT+TFWINFO指令
 * @return true 处理成功, false 处理失败
 * @note 收到此指令后会返回固件信息
 */
bool ldsAtCmdHandleTfwInfo(void)
{
    char response[128];
    const BootInfo_t* bootInfo = (const BootInfo_t*)BOOT_INFO_ADDR;
    
    // printf("Received AT+TFWINFO command\n");
    
    /* 检查引导信息是否有效 */
    if (bootInfo->magic == BOOT_INFO_MAGIC) {
        snprintf(response, sizeof(response), 
                "+TFWINFO:\r\nMode ID:%s\r\nFW ID:%s\r\nDate Code:20250807\r\nHW ID:1.00.00\r\n", 
                MODEL_ID, bootInfo->app1_version);
    } else {
        snprintf(response, sizeof(response), 
                "+TFWINFO:\r\nMode ID:%s\r\nFW ID:1.00.00\r\nDate Code:20250807\r\nHW ID:1.00.00\r\n", 
                MODEL_ID);
    }
    
    /* 发送固件信息响应 */
    ldsAtCmdSendResponse(response);
    ldsAtCmdSendResponse("OK");
    
    return true;
}

/**
 * @brief 检查是否收到BOOT=MCU指令
 * @return true 已收到, false 未收到
 */
bool ldsAtCmdIsBootMcuRequested(void)
{
    return g_bootMcuRequested;
}

/**
 * @brief 清除BOOT=MCU请求标志
 */
void ldsAtCmdClearBootMcuRequest(void)
{
    g_bootMcuRequested = false;
}

/**
 * @brief 解析AT指令
 * @return lds_at_cmd_result_t 解析结果
 * @note 该函数会检查串口是否有数据，如果有则解析AT指令并执行相应操作
 */
lds_at_cmd_result_t ldsAtCmdParse(void)
{
    uint8_t receivedByte;
    lds_at_cmd_t atCmd;
    lds_at_cmd_result_t result;

    /* 检查是否有数据可读 */
    if (!ldsAtCmdReceiveByte(&receivedByte)) {
        return LDS_AT_CMD_RESULT_NO_DATA;
    }

    /* 处理接收到的字符 */
    if (receivedByte == '\r' || receivedByte == '\n') {
        /* 收到回车或换行，处理完整的指令 */
        if (g_atCmdBufferIndex > 0) {
            g_atCmdBuffer[g_atCmdBufferIndex] = '\0';

            /* 解析指令 */
            result = ldsAtCmdParseString(g_atCmdBuffer, &atCmd);

            if (result == LDS_AT_CMD_RESULT_OK) {
                /* 根据指令类型执行相应操作 */
                switch (atCmd.type) {
                    case LDS_AT_CMD_BOOT_MCU:
                        if (ldsAtCmdHandleBootMcu()) {
                            result = LDS_AT_CMD_RESULT_OK;
                        } else {
                            result = LDS_AT_CMD_RESULT_ERROR;
                        }
                        break;

                    case LDS_AT_CMD_TFWINFO:
                        if (ldsAtCmdHandleTfwInfo()) {
                            result = LDS_AT_CMD_RESULT_OK;
                        } else {
                            result = LDS_AT_CMD_RESULT_ERROR;
                        }
                        break;

                    default:
                        ldsAtCmdSendResponse("ERROR");
                        result = LDS_AT_CMD_RESULT_INVALID;
                        break;
                }
            } else {
                /* 解析失败，发送错误响应 */
                ldsAtCmdSendResponse("ERROR");
            }

            /* 清空缓冲区 */
            g_atCmdBufferIndex = 0;
            memset(g_atCmdBuffer, 0, sizeof(g_atCmdBuffer));
        }
    } else if (receivedByte >= 0x20 && receivedByte <= 0x7E) {
        /* 可打印字符，添加到缓冲区 */
        if (g_atCmdBufferIndex < (LDS_AT_CMD_BUFFER_SIZE - 1)) {
            g_atCmdBuffer[g_atCmdBufferIndex++] = (char)receivedByte;
        } else {
            /* 缓冲区溢出，清空缓冲区 */
            g_atCmdBufferIndex = 0;
            memset(g_atCmdBuffer, 0, sizeof(g_atCmdBuffer));
            ldsAtCmdSendResponse("ERROR");
            return LDS_AT_CMD_RESULT_ERROR;
        }
    }
    /* 其他字符忽略 */

    return LDS_AT_CMD_RESULT_OK;
}
