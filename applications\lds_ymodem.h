/**
 * @file lds_ymodem.h
 * @brief YModem协议升级模块头文件
 * <AUTHOR>
 * @date 2024
 */

#ifndef __LDS_YMODEM_H__
#define __LDS_YMODEM_H__

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// YModem协议定义
#define YMODEM_SOH          0x01    // 128字节数据包头
#define YMODEM_STX          0x02    // 1024字节数据包头
#define YMODEM_EOT          0x04    // 传输结束
#define YMODEM_ACK          0x06    // 确认
#define YMODEM_NAK          0x15    // 否认
#define YMODEM_CAN          0x18    // 取消
#define YMODEM_C            0x43    // 'C' - CRC模式

#define YMODEM_PACKET_SIZE_128   128
#define YMODEM_PACKET_SIZE_1024  1024
#define YMODEM_PACKET_HEADER     3
#define YMODEM_PACKET_CRC        2
#define YMODEM_TIMEOUT_MS        6000
#define YMODEM_MAX_RETRY         10

typedef enum {
    YMODEM_OK = 0,
    YMODEM_ERROR,
    YMODEM_TIMEOUT,
    YMODEM_CANCEL,
    YMODEM_FILE_TOO_LARGE,
    YMODEM_CRC_ERROR
} lds_ymodem_result_t;

typedef struct {
    char filename[64];
    uint32_t filesize;
    uint32_t expected_crc32;
    char version[8];
} lds_ymodem_file_info_t;

/**
 * @brief 初始化YModem升级
 */
void ldsYmodemInit(void);

/**
 * @brief 开始YModem升级流程
 * @return lds_ymodem_result_t 升级结果
 */
lds_ymodem_result_t ldsYmodemUpgrade(void);

/**
 * @brief 解析文件名获取版本和CRC信息
 * @param filename 文件名
 * @param fileInfo 文件信息结构体
 * @return true 解析成功, false 解析失败
 */
bool ldsYmodemParseFilename(const char* filename, lds_ymodem_file_info_t* fileInfo);

#ifdef __cplusplus
}
#endif

#endif /* __LDS_YMODEM_H__ */