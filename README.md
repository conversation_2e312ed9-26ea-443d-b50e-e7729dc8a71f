# N32G452

## 简介
| 硬件      | 描述          |
| --------- | ------------- |
| 芯片型号  | N32G452VE  |
| CPU       | ARM Cortex M4 |
| 主频      | 144M          |
| 片内SRAM  | 144K |
| 片内FLASH | 512K         |


## 使用说明
编译需要先安装arm-none-eabi-gcc编译器，目前使用的是rt-thread env工具里的编译器
### 离线安装
下载并解压 [env-windows-v2.0.0-venv.7z](https://github.com/RT-Thread/env-windows/releases/download/v2.0.0/env-windows-v2.0.0-venv.7z) 到C盘根目录（目录结构为：C:\env-windows），双击 env.exe 进入 env 环境，进行首次使用环境初始化。
必须解压到C盘根目录，结构为：C:\env-windows\env.exe...

### 编译
``` shell
.\build.bat
```
生成的文件路径是build\n32g45x-boot.bin，带烧录地址信息的是build\n32g45x-boot.elf

## 注意事项



