/**
 * @file    lds_log.c
 * @brief   日志输出模块实现，提供UART日志输出功能
 * <AUTHOR>
 * @version v1.0.0
 * @date    2024
 *
 * @copyright Copyright (c) <PERSON><PERSON><PERSON> ~. All rights reserved.
 */

/* include files ------------------------------------------------------------ */
#include "lds_log.h"
#include <stdlib.h>
#include <stdarg.h>
#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <ctype.h>
#include <unistd.h>
#include <errno.h>
/* macro definition --------------------------------------------------------- */


/* typedef ------------------------------------------------------------------ */
#ifndef caddr_t
typedef char *caddr_t;  /**< 字符指针类型定义，用于系统调用 */
#endif

/* global variables --------------------------------------------------------- */


/* local variables ---------------------------------------------------------- */


/* local function declare --------------------------------------------------- */
static int ldsUsartLogSendByte(uint8_t ucData);

/* function prototypes ------------------------------------------------------ */
#undef errno
extern int errno;
extern int _end;

/**
 * @brief  系统调用：内存分配
 * @param  incr 内存增量
 * @return 分配的内存地址，失败返回(caddr_t)-1
 */
caddr_t _sbrk(int incr)
{
    static unsigned char *heap = NULL;
    unsigned char *prevHeap;

    if (heap == NULL) {
        heap = (unsigned char *)&_end;
    }

    prevHeap = heap;

    heap += incr;

    return (caddr_t)prevHeap;
}

/**
 * @brief  系统调用：创建文件链接（未实现）
 * @param  oldPath 原文件路径
 * @param  newPath 新文件路径
 * @return 始终返回-1
 */
int link(const char *oldPath, const char *newPath)
{
    (void)oldPath;  /* 避免未使用参数警告 */
    (void)newPath;  /* 避免未使用参数警告 */
    return -1;
}

/**
 * @brief  系统调用：关闭文件（未实现）
 * @param  file 文件描述符
 * @return 始终返回-1
 */
int _close(int file)
{
    (void)file;  /* 避免未使用参数警告 */
    return -1;
}

/**
 * @brief  系统调用：获取文件状态
 * @param  file 文件描述符
 * @param  st   文件状态结构体指针
 * @return 成功返回0
 */
int _fstat(int file, struct stat *st)
{
    (void)file;  /* 避免未使用参数警告 */
    if (st != NULL) {
        st->st_mode = S_IFCHR;
    }
    return 0;
}

/**
 * @brief  系统调用：检查是否为终端设备
 * @param  file 文件描述符
 * @return 始终返回1（是终端设备）
 */
int _isatty(int file)
{
    (void)file;  /* 避免未使用参数警告 */
    return 1;
}

/**
 * @brief  系统调用：文件定位（未实现）
 * @param  file 文件描述符
 * @param  ptr  偏移量
 * @param  dir  定位方向
 * @return 始终返回0
 */
int _lseek(int file, int ptr, int dir)
{
    (void)file;  /* 避免未使用参数警告 */
    (void)ptr;   /* 避免未使用参数警告 */
    (void)dir;   /* 避免未使用参数警告 */
    return 0;
}

/**
 * @brief  系统调用：程序异常终止
 */
void abort(void)
{
    /* 程序异常终止，进入无限循环 */
    while (1) {
        __NOP();
    }
}

/**
 * @brief  系统调用：写入数据到文件描述符
 * @param  fd      文件描述符
 * @param  pBuffer 数据缓冲区指针
 * @param  size    数据大小
 * @return 实际写入的字节数
 */
int _write(int fd, char *pBuffer, int size)
{
    uint32_t i;

    (void)fd;  /* 避免未使用参数警告 */

    if (pBuffer == NULL || size < 0) {
        return -1;
    }

    for (i = 0; i < (uint32_t)size; i++) {
        fputc((uint8_t)pBuffer[i], stdout);
    }
    return size;
}

/**
 * @brief  系统调用：从文件描述符读取数据（未实现）
 * @param  fd      文件描述符
 * @param  pBuffer 数据缓冲区指针
 * @param  size    缓冲区大小
 * @return 读取的字节数
 */
int _read(int fd, char *pBuffer, int size)
{
    (void)fd;      /* 避免未使用参数警告 */
    (void)pBuffer; /* 避免未使用参数警告 */
    (void)size;    /* 避免未使用参数警告 */
    return 0;  /* 未实现读取功能，返回0 */
}

/**
 * @brief  初始化UART日志输出功能
 * @return 成功返回0
 */
int ldsUartLogInit(void)
{
    GPIO_InitType gpioInitStructure;
    USART_InitType usartInitStructure;

    /* 关闭JTAG功能 */

    /* 使能AFIO和GPIOA时钟 */
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO | RCC_APB2_PERIPH_GPIOA, ENABLE);

    /* 使能USART1时钟 */
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_USART1, ENABLE);

    /* 配置USART1 TX (PA9)引脚为复用推挽输出 */
    gpioInitStructure.Pin        = GPIO_PIN_9;
    gpioInitStructure.GPIO_Mode  = GPIO_Mode_AF_PP;
    gpioInitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitPeripheral(GPIOA, &gpioInitStructure);

    /* 配置USART1 RX (PA10)引脚为浮空输入 */
    gpioInitStructure.Pin       = GPIO_PIN_10;
    gpioInitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_InitPeripheral(GPIOA, &gpioInitStructure);

    /* 配置USART1参数 */
    usartInitStructure.BaudRate            = 115200;
    usartInitStructure.WordLength          = USART_WL_8B;
    usartInitStructure.StopBits            = USART_STPB_1;
    usartInitStructure.Parity              = USART_PE_NO;
    usartInitStructure.HardwareFlowControl = USART_HFCTRL_NONE;
    usartInitStructure.Mode                = USART_MODE_RX | USART_MODE_TX;

    /* 初始化USART1 */
    USART_Init(USART1, &usartInitStructure);

    /* 使能USART1 */
    USART_Enable(USART1, ENABLE);

    /* 清除USART1标志位 */
    USART_ClrFlag(USART1, USART_FLAG_TXDE | USART_FLAG_TXC);

    return 0;
}

/**
 * @brief  通过USART发送单个字节
 * @param  ucData 要发送的字节数据
 * @return 成功返回0
 */
static int ldsUsartLogSendByte(uint8_t ucData)
{
    /* 等待发送数据寄存器为空 */
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXDE) == RESET) {
        __NOP();
    }
    /* 发送数据 */
    USART_SendData(USART1, ucData);
    return 0;
}

/**
 * @brief  通过USART发送数据缓冲区
 * @param  pucData 数据缓冲区指针
 * @param  uiLen   数据长度
 * @return 成功返回0，失败返回-1
 */
int ldsUsartLogSendBuffer(uint8_t *pucData, uint32_t uiLen)
{
    uint32_t uiIndex;

    if (pucData == NULL) {
        return -1;
    }

    for (uiIndex = 0; uiIndex < uiLen; uiIndex++) {
        ldsUsartLogSendByte(pucData[uiIndex]);
    }

    return 0;
}

/**
 * @brief  通过USART发送字符串
 * @param  pStr 字符串指针
 * @return 成功返回0，失败返回-1
 */
int ldsUsartLogSendString(char *pStr)
{
    uint32_t uiIndex;
    uint32_t strLen;

    if (pStr == NULL) {
        return -1;
    }

    strLen = strlen(pStr);
    for (uiIndex = 0; uiIndex < strLen; uiIndex++) {
        ldsUsartLogSendByte((uint8_t)pStr[uiIndex]);
    }

    return 0;
}

/**
 * @brief  格式化输出日志信息
 * @param  fmt 格式化字符串
 * @param  ... 可变参数列表
 * @return 成功返回0
 */
int ldsUsartLogOutput(char *fmt, ...)
{
    va_list args;

    if (fmt == NULL) {
        return -1;
    }

    va_start(args, fmt);
    vprintf(fmt, args);
    va_end(args);

    return 0;
}

/**
 * @brief  重定向fputc函数到USART输出
 * @param  ch 要输出的字符
 * @param  f  文件指针（未使用）
 * @return 输出的字符
 */
int fputc(int ch, FILE *f)
{
    (void)f;  /* 避免未使用参数警告 */
    ldsUsartLogSendByte((uint8_t)ch);
    return ch;
}
/**************************** END OF FILE *************************************/
