#ifndef __MAIN_H__
#define __MAIN_H__

#include <stdint.h>

#define MODEL_ID "AUX-EDUAudioProcessor-0001"

#define BOOT_INFO_MAGIC   0xA5A5A5A5  // 魔术字，用于判断该区域是否有效

typedef enum {
    APP_STATE_NORMAL = 0x01,
    APP_STATE_UPDATE = 0x02
} LDS_APP_STATE_E;
 
typedef struct {
    uint32_t magic;           // 魔术字，用于校验
    LDS_APP_STATE_E state;    // 启动状态：正常启动或升级
    uint32_t app1_crc32;        // APP1 固件的 CRC32 校验值
    uint32_t app1_size;         // APP1 固件的大小
    char app1_version[8];      // APP1 版本号
    uint32_t app2_crc32;        // APP2 固件的 CRC32 校验值
    uint32_t app2_size;       // APP2 固件的大小
    char app2_version[8];      // APP2 版本号
    uint32_t crc32;           // boot 信息检验值
} BootInfo_t;

uint32_t ldsUtilCheckCrc32(const uint8_t *data, uint32_t len);
void delayMs(uint32_t ms);
void hwUsDelay(uint32_t us);
#endif // !__MAIN_H__
