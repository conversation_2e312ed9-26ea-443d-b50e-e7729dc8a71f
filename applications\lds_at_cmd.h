/**
 * @file lds_at_cmd.h
 * @brief AT指令解析模块头文件
 * @details 提供AT指令解析功能，支持AT+BOOT=MCU和AT+TFWINFO指令
 * <AUTHOR>
 * @date 2024
 */

#ifndef __LDS_AT_CMD_H__
#define __LDS_AT_CMD_H__

#ifdef __cplusplus
extern "C" {
#endif

/* 相关头文件 */
#include <stdint.h>
#include <stdbool.h>

/* 宏定义 */
#define LDS_AT_CMD_MAX_LENGTH       64      /**< AT指令最大长度 */
#define LDS_AT_CMD_BUFFER_SIZE      128     /**< AT指令缓冲区大小 */
#define LDS_AT_CMD_TIMEOUT_MS       100     /**< AT指令接收超时时间(毫秒) */

/* AT指令类型枚举 */
typedef enum {
    LDS_AT_CMD_UNKNOWN = 0,     /**< 未知指令 */
    LDS_AT_CMD_BOOT_MCU,        /**< AT+BOOT=MCU指令 */
    LDS_AT_CMD_TFWINFO,         /**< AT+TFWINFO指令 */
    LDS_AT_CMD_MAX
} lds_at_cmd_type_t;

/* AT指令解析结果枚举 */
typedef enum {
    LDS_AT_CMD_RESULT_OK = 0,       /**< 解析成功 */
    LDS_AT_CMD_RESULT_ERROR,        /**< 解析错误 */
    LDS_AT_CMD_RESULT_TIMEOUT,      /**< 接收超时 */
    LDS_AT_CMD_RESULT_NO_DATA,      /**< 无数据 */
    LDS_AT_CMD_RESULT_INVALID       /**< 无效指令 */
} lds_at_cmd_result_t;

/* AT指令结构体 */
typedef struct {
    lds_at_cmd_type_t type;                     /**< 指令类型 */
    char cmd[LDS_AT_CMD_MAX_LENGTH];            /**< 指令内容 */
    char param[LDS_AT_CMD_MAX_LENGTH];          /**< 参数内容 */
} lds_at_cmd_t;

/* 函数声明 */

/**
 * @brief 初始化AT指令解析模块
 * @return 0 成功, 其他 失败
 */
int ldsAtCmdInit(void);

/**
 * @brief 解析AT指令
 * @return lds_at_cmd_result_t 解析结果
 * @note 该函数会检查串口是否有数据，如果有则解析AT指令并执行相应操作
 */
lds_at_cmd_result_t ldsAtCmdParse(void);

/**
 * @brief 处理AT+BOOT=MCU指令
 * @return true 处理成功, false 处理失败
 * @note 收到此指令后会启动YModem升级流程
 */
bool ldsAtCmdHandleBootMcu(void);

/**
 * @brief 处理AT+TFWINFO指令
 * @return true 处理成功, false 处理失败
 * @note 收到此指令后会返回固件信息
 */
bool ldsAtCmdHandleTfwInfo(void);

/**
 * @brief 发送AT指令响应
 * @param response 响应内容
 * @return true 发送成功, false 发送失败
 */
bool ldsAtCmdSendResponse(const char* response);

/**
 * @brief 检查是否收到BOOT=MCU指令
 * @return true 已收到, false 未收到
 */
bool ldsAtCmdIsBootMcuRequested(void);

/**
 * @brief 清除BOOT=MCU请求标志
 */
void ldsAtCmdClearBootMcuRequest(void);

#ifdef __cplusplus
}
#endif

#endif /* __LDS_AT_CMD_H__ */
